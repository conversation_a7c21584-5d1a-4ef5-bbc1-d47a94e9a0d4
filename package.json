{"name": "unibest-demo", "type": "commonjs", "version": "1.5.0", "description": "unibest - 最好的 uniapp 开发模板", "author": {"name": "feige996", "zhName": "菲鸽", "email": "<EMAIL>", "github": "https://github.com/feige996", "gitee": "https://gitee.com/feige996"}, "license": "MIT", "repository": "https://github.com/feige996/unibest", "repository-gitee": "https://gitee.com/feige996/unibest", "bugs": {"url": "https://github.com/feige996/unibest/issues"}, "homepage": "https://feige996.github.io/hello-unibest/", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev": "uni", "dev-dev": "uni --mode development", "dev-test": "uni --mode test", "dev-prod": "uni --mode production", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp": "uni -p mp-weixin", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp": "uni build -p mp-weixin", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "node ./shell/postinstall.js & git init && husky install ", "type-check": "vue-tsc --noEmit"}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix --allow-empty-input"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@climblee/uv-ui": "^1.1.20", "@dcloudio/uni-app": "3.0.0-4000820240401001", "@dcloudio/uni-app-plus": "3.0.0-4000820240401001", "@dcloudio/uni-components": "3.0.0-4000820240401001", "@dcloudio/uni-h5": "3.0.0-4000820240401001", "@dcloudio/uni-mp-alipay": "3.0.0-4000820240401001", "@dcloudio/uni-mp-baidu": "3.0.0-4000820240401001", "@dcloudio/uni-mp-jd": "3.0.0-4000820240401001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4000820240401001", "@dcloudio/uni-mp-lark": "3.0.0-4000820240401001", "@dcloudio/uni-mp-qq": "3.0.0-4000820240401001", "@dcloudio/uni-mp-toutiao": "3.0.0-4000820240401001", "@dcloudio/uni-mp-weixin": "3.0.0-4000820240401001", "@dcloudio/uni-mp-xhs": "3.0.0-4000820240401001", "@dcloudio/uni-quickapp-webview": "3.0.0-4000820240401001", "@dcloudio/uni-ui": "1.5.0", "dayjs": "1.11.10", "echarts": "^5.6.0", "lodash-es": "^4.17.21", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "vue": "3.3.11", "vue-i18n": "^9.10.2", "wot-design-uni": "^1.9.1", "z-paging": "^2.7.8"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4000820240401001", "@dcloudio/uni-cli-shared": "3.0.0-4000820240401001", "@dcloudio/uni-stacktracey": "3.0.0-4000820240401001", "@dcloudio/vite-plugin-uni": "3.0.0-4000820240401001", "@iconify-json/carbon": "^1.1.27", "@ttou/uv-typings": "^1.10.2", "@types/node": "^20.11.5", "@types/wechat-miniprogram": "^3.4.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@uni-helper/uni-ui-types": "^0.5.11", "@uni-helper/vite-plugin-uni-components": "^0.0.8", "@uni-helper/vite-plugin-uni-layouts": "^0.1.7", "@uni-helper/vite-plugin-uni-manifest": "^0.2.3", "@uni-helper/vite-plugin-uni-pages": "^0.2.15", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@unocss/preset-legacy-compat": "0.58.9", "@vue/runtime-core": "^3.3.13", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.16", "commitlint": "^18.4.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-vue": "^9.19.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.11.0", "sass": "1.77.8", "stylelint": "^16.0.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.4.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^35.0.0", "stylelint-config-standard-scss": "^12.0.0", "stylelint-prettier": "^5.0.0", "terser": "^5.26.0", "typescript": "^4.9.4", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.17.2", "vite": "4.3.5", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mock": "^3.0.1", "vite-plugin-restart": "^0.4.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^5.1.0", "vue-tsc": "^1.8.25"}}