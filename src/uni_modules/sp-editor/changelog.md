## 1.3.7（2024-05-10）
1. 修复添加超链接后，不触发input更新当前最新内容的bug
## 1.3.6（2024-05-09）
1. 文档迁移
## 1.3.5（2024-05-09）
1. 所有事件携带编辑器id参数，以便循环时能区分处理
2. 更新示例工程
## 1.3.4（2024-05-08）
1. 更新示例工程
2. 新增editorId参数
## 1.3.3（2024-03-22）
1. 修复微信小程序长按无法粘贴的问题
## 1.3.2（2024-03-14）
1. 更新了toolbar样式与配置，见文档
2. 更新示例工程，媒体查询响应式写法
3. 优化了只读模式效果，开启只读模式后，文章内容的超链接可正常点击并跳转
## 1.3.1（2024-03-14）
1. 优化了只读功能，开启只读后自动隐藏工具栏
2. 更新示例工程
## 1.3.0（2024-03-07）
1. 新增addLink的emit事件
## 1.2.9（2024-02-23）
1. 更新文档
## 1.2.8（2024-02-23）
1. 新增了添加超链接的工具，toolbar中link字段，默认开启
2. 优化了部分逻辑
3. 更新文档、更新示例工程
## 1.2.7（2024-02-23）
1. 更新文档，更新示例工程
2. 添加toolbar中图标字体大小可配置项
## 1.2.6（2024-02-22）
1. 添加导出工具按钮，可将当前已编辑的html导出至页面解析
2. 超链接工具按钮正在尝试开发中(貌似目前官方不支持)
## 1.2.5（2024-02-19）
1. 更新示例工程(吸顶写法)
2. 完善调色板功能
## 1.2.4（2024-02-18）
1. 修复工具栏颜色按钮底色动态切换问题
## 1.2.3（2024-02-18）
1. 更新示例工程
## 1.2.2（2024-02-18）
1. 删除log调试打印
## 1.2.1（2024-02-18）
1. 修复了颜色图标不会动态切换的问题
## 1.2.0（2024-02-18）
1. 修复选择颜色时会将所选文字删除的bug
## 1.1.9（2024-02-04）
1. 更新示例工程
## 1.1.8（2024-02-04）
1. 文档修改
## 1.1.7（2024-02-04）
1. 新增toolbar配置项，可自由配置工具栏工具列表
2. 移除组件内原templates属性，默认初始化编辑器内容请看文档使用方式示例
3. 更新文档
## 1.1.6（2024-01-31）
1. 更好的兼容vue2了，修复在vue2下高度可能超出的问题
2. 示例工程兼容vue2
## 1.1.5（2024-01-30）
1. 修复工具栏字体按钮无效的问题
## 1.1.4（2024-01-30）
1. 解决默认初始化内容时前缀空格或缩进无效的问题
2. 解决点击工具栏高亮状态后输入内容时便失去高亮的bug
3. 更新示例工程
## 1.1.3（2024-01-23）
1. 重写高度动态计算逻辑，现在对不同屏幕尺寸的适应性更强了
## 1.1.2（2024-01-17）
1. 修复分割线会生成多条的问题
## 1.1.1（2024-01-15）
1. 更新文档
## 1.1.0（2024-01-15）
1. insertText方法在插入内容的时候会移动光标聚焦，导致焦点回滚到视口处
2. 更新示例工程
## 1.0.9（2024-01-04）
1. 更新文档
## 1.0.8（2024-01-04）
1. 修复h5端官方cdn请求失败的问题，详见问答贴：https://ask.dcloud.net.cn/article/40900
## 1.0.7（2024-01-03）
1.  移除v-bind="$attrs"，该写法在微信小程序不支持
## 1.0.6（2023-12-29）
1. 更新文档
## 1.0.5（2023-12-29）
1. 更新了init方法，可以使用返回的editor实例尽情自定义
2. 组件在<editor>上添加v-bind="$attrs"属性穿透，可以使用原editor参数，官方文档：https://uniapp.dcloud.net.cn/component/editor.html
## 1.0.4（2023-12-29）
1. 优化了切换文字和背景颜色是，可能会导致切换后不生效的问题
2. 修复在部分设备上的微信小程序中可能会存在颜色版无法正常滑动的问题
3. 更友好的交互体验：添加图标悬停字样描述、添加格式化文本弹窗确认
4. 有插入视频的需求，暂时可能无法实现，官方给予的回复是：目前各端的eidtor组件都不能直接插入视频，编辑时可以采用视频封面占位，并在图片中保存视频信息，在预览时再还原为视频。
## 1.0.3（2023-10-13）
	1. 更新readme文档
	2. 更新调整组件示例项目，添加插件代码中部分注释
## 1.0.2（2023-10-13）
	1. 更新uni_modules规范，可一键导入组件
	2. 更新组件示例项目（包括使用uniCloud.uploadFile多选上传图片示例方法）
## 1.0.1（2023-10-12）
	1. 修复小程序中自动聚焦滚动到富文本组件区域的bug
	2. 略微调整了富文本上方toolbar工具栏中按钮的大小尺寸
## 1.0.0（2023-9-19）
	1. 新增字体与背景颜色板
	2. 可自定义预设内容模板
	3. 解决官方样例在小程序和app部分报错不兼容的问题
	4. 可配合云存储上传富文本中插入的图片 本质上是基于官方内置富文本editor组件改版封装，所以官方有的功能都有，官方能兼容的也都兼容

